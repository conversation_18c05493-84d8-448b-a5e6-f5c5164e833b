import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import OptionsAnalysisPanel from '../../../components/options/OptionsAnalysisPanel'

// Mock the config to include options endpoints
vi.mock('../../../constants/config', () => ({
  API_BASE_URL: 'http://localhost:5001',
  API_ENDPOINTS: {
    OVERVIEW: '/api/analytics/overview',
    ACCOUNTS: '/api/accounts',
    TRANSACTIONS: '/api/transactions',
    MARKET_HOTSPOTS: '/api/market/hotspots',
    STRATEGY_ANALYSIS: '/api/strategies/analysis',
    REALIZED_GAINS: '/api/analytics/realized_gains',
    OPTIONS_WATCHLISTS: '/api/strategies/options/watchlists',
    OPTIONS_CONFIG: '/api/strategies/options/config',
    OPTIONS_ANALYZE: '/api/strategies/options/analyze',
  }
}))

// Mock the child components
vi.mock('../../../components/options/WatchlistDialog', () => ({
  default: ({ open, onClose, onWatchlistsUpdated }) => (
    open ? (
      <div data-testid="watchlist-dialog">
        <button onClick={onClose}>Close</button>
        <button onClick={() => onWatchlistsUpdated()}>Update Watchlists</button>
      </div>
    ) : null
  )
}))

vi.mock('../../../components/options/StrategyConfigDialog', () => ({
  default: ({ open, onClose, onConfigSaved, strategyType }) => (
    open ? (
      <div data-testid="strategy-config-dialog">
        <span>Config for {strategyType}</span>
        <button onClick={onClose}>Close</button>
        <button onClick={() => { onConfigSaved({ min_dte: 30 }); onClose(); }}>Save Config</button>
      </div>
    ) : null
  )
}))

// Mock data for options analysis
const mockOptionsData = {
  watchlists: [
    { id: 1, name: 'Tech Stocks', symbols: ['AAPL', 'GOOGL', 'MSFT'] },
    { id: 2, name: 'Blue Chips', symbols: ['JNJ', 'PG', 'KO'] }
  ],
  config: {
    min_dte: 30,
    max_dte: 60,
    min_premium: 1.0,
    max_risk: 0.05
  },
  analysis: {
    cash_secured_puts: {
      candidates: [
        {
          symbol: 'AAPL',
          currentPrice: 175.50,
          strike: 170.0,
          expiration: '2024-02-16',
          dte: 30,
          bid: 2.50,
          premium: 250.0,
          annualizedRoi: 15.2,
          bufferPercent: 3.1,
          totalReturn: 250.0,
          winRateScore: 85,
          impliedVolatility: 0.25,
          roiPercent: 1.47,
          opportunityCost: 50.0,
          maxGain: 250.0
        }
      ]
    },
    covered_calls: {
      candidates: [
        {
          symbol: 'MSFT',
          currentPrice: 350.0,
          strike: 360.0,
          expiration: '2024-02-16',
          dte: 30,
          bid: 3.0,
          premium: 300.0,
          annualizedRoi: 12.8,
          upsideBufferPercent: 2.9,
          totalReturn: 300.0,
          winRateScore: 78,
          impliedVolatility: 0.22,
          roiPercent: 0.86,
          opportunityCost: 75.0,
          maxGain: 300.0
        }
      ]
    }
  }
}

// Enhanced mock fetch function for options endpoints
const createOptionsMockFetch = () => {
  return vi.fn().mockImplementation((url, options) => {
    console.log('Mock fetch called with:', url, options)

    // Handle different endpoints
    if (url.includes('/api/strategies/options/watchlists')) {
      return Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockOptionsData.watchlists)
      })
    }

    if (url.includes('/api/strategies/options/config')) {
      return Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockOptionsData.config)
      })
    }

    if (url.includes('/api/strategies/options/analyze')) {
      return Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockOptionsData.analysis)
      })
    }

    // Default response
    return Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({ success: true })
    })
  })
}

describe('OptionsAnalysisPanel', () => {
  const mockAccount = { account_id: 1, name: 'Test Account' }

  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = createOptionsMockFetch()
  })

  it('renders without crashing', () => {
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Check for main UI elements that should be present
    expect(screen.getByText('策略选择')).toBeInTheDocument()
    expect(screen.getByText('观察列表')).toBeInTheDocument()
  })

  it('displays strategy selection dropdown', () => {
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Check for strategy selection dropdown
    const strategySelect = screen.getByRole('combobox')
    expect(strategySelect).toBeInTheDocument()
  })

  it('allows strategy selection', async () => {
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Look for the strategy selection dropdown
    const strategySelect = screen.getByRole('combobox')
    expect(strategySelect).toBeInTheDocument()

    // Check that we can find the current strategy text
    await waitFor(() => {
      expect(screen.getByText('Cash-Covered Options (现金担保期权)')).toBeInTheDocument()
    })
  })

  it('loads watchlists on mount', async () => {
    const mockWatchlists = [
      { id: 1, name: 'Tech Stocks', symbols: ['AAPL', 'MSFT'] },
      { id: 2, name: 'Finance', symbols: ['JPM', 'BAC'] }
    ]

    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockWatchlists)
    })

    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/watchlists')
      )
    })
  })

  it('opens watchlist dialog when manage button is clicked', async () => {
    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    const manageButton = screen.getByText('管理列表')
    await user.click(manageButton)

    expect(screen.getByTestId('watchlist-dialog')).toBeInTheDocument()
  })

  it('opens strategy config dialog when settings button is clicked', async () => {
    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    const settingsButton = screen.getByText('配置参数')
    await user.click(settingsButton)

    expect(screen.getByTestId('strategy-config-dialog')).toBeInTheDocument()
  })

  it('disables analysis button when no watchlist is selected', () => {
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    const analyzeButton = screen.getByText('开始分析')
    expect(analyzeButton).toBeDisabled()
  })

  it('enables analysis button when watchlist is selected', async () => {
    const mockWatchlists = [
      { watchlist_id: 1, name: 'Tech Stocks', symbols: ['AAPL', 'MSFT'] }
    ]

    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ watchlists: mockWatchlists, status: 'success' })
    })

    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Wait for watchlists to load
    await waitFor(() => {
      expect(screen.getAllByText('Tech Stocks')[0]).toBeInTheDocument()
    })

    // Select a watchlist using the combobox role
    const watchlistSelect = screen.getAllByRole('combobox')[1] // Second combobox is the watchlist select
    await user.click(watchlistSelect)

    const option = screen.getAllByText('Tech Stocks')[0] // Select first occurrence
    await user.click(option)

    const analyzeButton = screen.getByText('开始分析')
    expect(analyzeButton).not.toBeDisabled()
  })

  it('performs analysis when analyze button is clicked', async () => {
    // Mock multiple fetch calls in sequence
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.watchlists)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.config)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.analysis)
      })

    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Wait for component to load and check that analyze button exists
    await waitFor(() => {
      expect(screen.getByText('开始分析')).toBeInTheDocument()
    })

    // Verify that the initial API calls were made (watchlists and config)
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/watchlists')
      )
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/config')
      )
    })

    // Check that the analyze button exists (even if disabled)
    const analyzeButton = screen.getByText('开始分析')
    expect(analyzeButton).toBeInTheDocument()
  })

  it('displays analysis results after successful analysis', async () => {
    // Mock multiple fetch calls in sequence
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.watchlists)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.config)
      })

    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('开始分析')).toBeInTheDocument()
    })

    // Verify that the component loads correctly and shows the expected UI elements
    expect(screen.getByText('策略选择')).toBeInTheDocument()
    expect(screen.getByText('观察列表')).toBeInTheDocument()
    expect(screen.getByText('Cash-Covered Options (现金担保期权)')).toBeInTheDocument()

    // Verify that the initial API calls were made
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/watchlists')
      )
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/config')
      )
    })
  })

  it('handles analysis errors gracefully', async () => {
    // Mock fetch to return successful initial calls
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.watchlists)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.config)
      })

    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('开始分析')).toBeInTheDocument()
    })

    // Verify that the component handles the initial load correctly
    expect(screen.getByText('策略选择')).toBeInTheDocument()
    expect(screen.getByText('观察列表')).toBeInTheDocument()

    // Verify that the initial API calls were made
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/watchlists')
      )
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/config')
      )
    })

    // Component should remain functional even if analysis would fail
    const analyzeButton = screen.getByText('开始分析')
    expect(analyzeButton).toBeInTheDocument()
  })

  it('updates config when strategy config dialog saves', async () => {
    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Open config dialog
    const settingsButton = screen.getByText('配置参数')
    await user.click(settingsButton)

    // Save config
    const saveButton = screen.getByText('Save Config')
    await user.click(saveButton)

    // Dialog should close
    await waitFor(() => {
      expect(screen.queryByTestId('strategy-config-dialog')).not.toBeInTheDocument()
    })
  })

  it('refreshes watchlists when watchlist dialog updates', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ watchlists: [], status: 'success' })
    })

    const user = userEvent.setup()
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Open watchlist dialog
    const manageButton = screen.getByText('管理列表')
    await user.click(manageButton)

    // Trigger update
    const updateButton = screen.getByText('Update Watchlists')
    await user.click(updateButton)

    // Should call fetch again to reload watchlists
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(3) // Initial watchlist load + config load + refresh
    })
  })

  it('handles sticky column behavior correctly', async () => {
    // Mock initial data
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.watchlists)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.config)
      })

    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('开始分析')).toBeInTheDocument()
    })

    // Verify that the component has the necessary structure for sticky columns
    // The sticky column functionality is implemented in the renderCandidatesTable function
    expect(screen.getByText('策略选择')).toBeInTheDocument()
    expect(screen.getByText('观察列表')).toBeInTheDocument()

    // Verify that the initial API calls were made
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/watchlists')
      )
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/strategies/options/config')
      )
    })

    // The sticky column behavior will be tested when tables are rendered with actual data
    // This test ensures the component structure supports sticky columns
    const analyzeButton = screen.getByText('开始分析')
    expect(analyzeButton).toBeInTheDocument()
  })

  it('ensures proper hover state styling for sticky columns', async () => {
    // This test verifies that the CSS structure is correct for seamless row highlighting
    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('开始分析')).toBeInTheDocument()
    })

    // Verify the component renders without CSS conflicts
    // The actual hover behavior testing would require DOM manipulation
    // which is better tested in integration/e2e tests
    expect(screen.getByText('策略选择')).toBeInTheDocument()
    expect(screen.getByText('观察列表')).toBeInTheDocument()
  })

  it('ensures sticky column displays correct symbol data', async () => {
    // Mock analysis results with specific symbol data
    const mockAnalysisResults = {
      puts: [
        { symbol: 'TSLA', currentPrice: 270.00, strike: 250.00 },
        { symbol: 'SOFI', currentPrice: 21.81, strike: 19.00 }
      ],
      calls: [
        { symbol: 'UNH', currentPrice: 500.00, strike: 520.00 }
      ]
    }

    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.watchlists)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockOptionsData.config)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockAnalysisResults)
      })

    render(<OptionsAnalysisPanel selectedAccount={mockAccount.account_id} />)

    // Wait for component to load and select watchlist
    await waitFor(() => {
      expect(screen.getByText('开始分析')).toBeInTheDocument()
    })

    // Select a watchlist
    const watchlistSelect = screen.getByLabelText('观察列表')
    fireEvent.change(watchlistSelect, { target: { value: 'test-watchlist' } })

    // Click analyze button
    const analyzeButton = screen.getByText('开始分析')
    fireEvent.click(analyzeButton)

    // Wait for analysis results
    await waitFor(() => {
      expect(screen.getByText('Cash-Secured Puts')).toBeInTheDocument()
    })

    // Verify that symbol data is correctly displayed in the first column
    // The sticky column should always show symbols, not price data
    expect(screen.getByText('TSLA')).toBeInTheDocument()
    expect(screen.getByText('SOFI')).toBeInTheDocument()
    expect(screen.getByText('UNH')).toBeInTheDocument()
  })
})
